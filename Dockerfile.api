# ==============================
# Build Stage
# ==============================
FROM node:18-slim AS builder

# Install build dependencies for native modules
RUN apt-get update && apt-get install -y \
    python3 \
    make \
    g++ \
    openssl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# Copy package and tsconfig files
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies (including dev for Prisma + TypeScript build)
RUN npm ci && npm cache clean --force

# Copy source, scripts, prisma
COPY src ./src
COPY scripts ./scripts
COPY prisma ./prisma
COPY start-api.sh ./

# ✅ Generate Prisma Client BEFORE build
RUN npx prisma generate

# ✅ Build TypeScript → JavaScript
RUN npm run build


# ==============================
# Production Stage
# ==============================
FROM node:18-slim AS production

# Install minimal runtime dependencies
RUN apt-get update && apt-get install -y \
    dumb-init \
    openssl \
    && groupadd -g 1001 nodejs \
    && useradd -r -u 1001 -g nodejs nodejs \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

# ✅ Copy only package files for production install
COPY --from=builder /app/package*.json ./

# ✅ Install production dependencies
RUN npm ci --only=production && npm cache clean --force

# ✅ Add Prisma CLI from node_modules to PATH
ENV PATH=/app/node_modules/.bin:$PATH

# ✅ Copy Prisma schema & migrations
COPY --from=builder /app/prisma ./prisma

# ✅ Prisma client copied from builder stage

# ✅ Copy built app and scripts
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules/.prisma ./node_modules/.prisma
COPY --from=builder /app/node_modules/@prisma ./node_modules/@prisma
COPY --from=builder /app/start-api.sh ./
RUN chmod +x start-api.sh

# ✅ Set ownership and switch to non-root user
RUN chown -R nodejs:nodejs /app
USER nodejs

EXPOSE 8080
ENV SERVICE_TYPE=api
ENV HOST=0.0.0.0

# Start the API
CMD ["dumb-init", "./start-api.sh"]
