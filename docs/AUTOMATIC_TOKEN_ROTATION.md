# Automatic JWT Token Rotation System

## Overview

This system implements **automatic token rotation** instead of manual logout. Every time a refresh token is used, it's automatically replaced with a new one, providing enhanced security without requiring user intervention.

## How It Works

### 🔄 **Automatic Rotation Flow**

1. **User logs in** → Gets access token (15min) + refresh token (7 days)
2. **Access token expires** → Client gets 401 error
3. **Client calls refresh** → Server automatically:
   - Validates old refresh token
   - Generates new access token (15min)
   - Generates new refresh token (7 days)
   - **Revokes old refresh token**
   - Returns both new tokens
4. **Process repeats** → No manual logout needed

### 🛡️ **Security Benefits**

- **Token Rotation**: Each refresh token can only be used once
- **Reduced Attack Window**: Stolen tokens become invalid quickly
- **Grace Period**: 24-hour window for expired tokens (prevents user lockout)
- **Automatic Cleanup**: Expired tokens are cleaned up automatically
- **No Manual Logout**: Tokens naturally expire and rotate

## Configuration

```typescript
// In src/utils/auth.ts
export const TOKEN_ROTATION_CONFIG = {
  ALWAYS_ROTATE: true,           // Always rotate on refresh
  GRACE_PERIOD_HOURS: 24,        // 24h grace period after expiry
  MAX_TOKENS_PER_USER: 5,        // Max concurrent sessions (0 = unlimited)
  AUTO_CLEANUP: true             // Auto cleanup expired tokens
}
```

## API Endpoints

### 1. Login (Returns Both Tokens)
```
POST /api/auth/login
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJ...",     // 15 minutes
    "refreshToken": "abc123...", // 7 days
    "user": { ... }
  }
}
```

### 2. Refresh (Automatic Rotation)
```
POST /api/auth/refresh
```

**Request:**
```json
{
  "refreshToken": "abc123..."
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "accessToken": "eyJ...",     // NEW 15-minute token
    "refreshToken": "xyz789..."  // NEW 7-day token (old one revoked)
  }
}
```

## Token Lifecycle

### 📅 **Timeline Example**

```
Day 0: Login
├── Access Token: expires in 15 minutes
└── Refresh Token: expires in 7 days

Day 0 + 15min: Access token expires
├── Client calls /refresh with refresh token
├── Server generates NEW access token (15min)
├── Server generates NEW refresh token (7 days)
└── Old refresh token is REVOKED

Day 7: Original refresh token would expire
├── But it was already revoked and replaced
└── Current refresh token has 7 days from last refresh

Day 8: Grace period (if token expired but not refreshed)
├── User can still refresh within 24h grace period
├── After 24h grace period: must login again
└── Grace period prevents accidental lockouts
```

## Error Handling

### 🚨 **Error Scenarios**

1. **Access Token Expired**
   ```json
   {
     "success": false,
     "message": "Access token expired",
     "statusCode": 401
   }
   ```
   **Action**: Client should call `/refresh`

2. **Refresh Token Expired (Within Grace Period)**
   ```json
   {
     "success": true,
     "data": {
       "accessToken": "...",
       "refreshToken": "..."
     }
   }
   ```
   **Action**: Rotation succeeds, security event logged

3. **Refresh Token Expired (Beyond Grace Period)**
   ```json
   {
     "success": false,
     "message": "Refresh token expired - please login again",
     "statusCode": 401
   }
   ```
   **Action**: Client should redirect to login

4. **Refresh Token Already Used (Revoked)**
   ```json
   {
     "success": false,
     "message": "Refresh token revoked",
     "statusCode": 401
   }
   ```
   **Action**: Client should redirect to login

## Client Implementation

### 📱 **JavaScript Example**

```javascript
class TokenManager {
  constructor() {
    this.accessToken = localStorage.getItem('accessToken')
    this.refreshToken = localStorage.getItem('refreshToken')
  }

  async apiCall(url, options = {}) {
    // Try with current access token
    let response = await this.makeRequest(url, options)
    
    // If access token expired, refresh and retry
    if (response.status === 401) {
      const refreshed = await this.refreshTokens()
      if (refreshed) {
        response = await this.makeRequest(url, options)
      } else {
        // Refresh failed, redirect to login
        this.redirectToLogin()
        return
      }
    }
    
    return response
  }

  async makeRequest(url, options) {
    return fetch(url, {
      ...options,
      headers: {
        ...options.headers,
        'Authorization': `Bearer ${this.accessToken}`
      }
    })
  }

  async refreshTokens() {
    try {
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ refreshToken: this.refreshToken })
      })

      if (response.ok) {
        const data = await response.json()
        this.accessToken = data.data.accessToken
        this.refreshToken = data.data.refreshToken
        
        // Store new tokens
        localStorage.setItem('accessToken', this.accessToken)
        localStorage.setItem('refreshToken', this.refreshToken)
        
        return true
      } else {
        // Refresh failed
        this.clearTokens()
        return false
      }
    } catch (error) {
      this.clearTokens()
      return false
    }
  }

  clearTokens() {
    this.accessToken = null
    this.refreshToken = null
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
  }

  redirectToLogin() {
    this.clearTokens()
    window.location.href = '/login'
  }
}
```

## Security Features

### 🔒 **Built-in Protections**

1. **Single Use Tokens**: Each refresh token can only be used once
2. **Atomic Rotation**: Old token revoked and new token created atomically
3. **Grace Period**: Prevents accidental user lockout from network issues
4. **Background Cleanup**: Expired tokens automatically removed
5. **Security Logging**: All token events logged for monitoring
6. **Concurrent Session Limits**: Optional limit on active tokens per user

## Monitoring & Maintenance

### 📊 **What to Monitor**

- Grace period usage (indicates potential issues)
- Token rotation frequency
- Failed refresh attempts
- Cleanup statistics

### 🧹 **Automatic Maintenance**

- Expired tokens cleaned up on each refresh
- Background cleanup runs automatically
- No manual intervention required

## Migration from Manual Logout

If you had manual logout before:

1. **Remove logout buttons** from UI (optional)
2. **Update client code** to handle automatic rotation
3. **Tokens expire naturally** - no action needed
4. **Users stay logged in** as long as they use the app

The system is **backward compatible** - existing tokens will work and rotate automatically.
