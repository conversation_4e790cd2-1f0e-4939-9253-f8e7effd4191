# Development Docker Deployment - Development environment with hot reload

services:
  # PostgreSQL Database (Development)
  db:
    image: postgres:16
    container_name: getrankt-db-dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: ${DB_USER}
      POSTGRES_PASSWORD: ${DB_PASSWORD}
      POSTGRES_DB: ${DB_NAME}
      POSTGRES_HOST_AUTH_METHOD: md5
    volumes:
      - pgdata_dev:/var/lib/postgresql/data
    ports:
      - "127.0.0.1:${DB_EXTERNAL_PORT}:${DB_PORT}"  # Different port to avoid conflicts with local/production
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    networks:
      - getrankt-dev-network

  # # Redis for vote processing (Development)
  # redis:
  #   image: redis:7-alpine
  #   container_name: getrankt-redis-dev
  #   restart: unless-stopped
  #   command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
  #   volumes:
  #     - redis_data_dev:/data
  #   ports:
  #     - "127.0.0.1:${REDIS_EXTERNAL_PORT}:${REDIS_PORT}"  # Different port to avoid conflicts
  #   healthcheck:
  #     test: ["CMD", "redis-cli", "ping"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 3
  #     start_period: 10s
  #   networks:
  #     - getrankt-dev-network

  # API Server (Development with hot reload)
  api-server:
    build:
      context: .
      dockerfile: Dockerfile.dev
      target: development
    container_name: getrankt-api-dev
    restart: unless-stopped
    command: ["npm", "run", "dev"]
    env_file:
      - .env
    environment:
      - NODE_ENV=${NODE_ENV}
      - PORT=${PORT}
      - HOST=${HOST}
      - SERVICE_TYPE=api
      # Database configuration
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - DB_NAME=${DB_NAME}
      - DB_SCHEMA=${DB_SCHEMA}
      - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:${DB_PORT}/${DB_NAME}?schema=${DB_SCHEMA}
      - DB_CONNECTION_LIMIT=${DB_CONNECTION_LIMIT}
      - DB_POOL_TIMEOUT=${DB_POOL_TIMEOUT}
      # Redis configuration
      # - REDIS_URL=${REDIS_URL}
      # - REDIS_RETRY_ATTEMPTS=${REDIS_RETRY_ATTEMPTS}
      # - REDIS_RETRY_DELAY=${REDIS_RETRY_DELAY}
      # - REDIS_CONNECTION_TIMEOUT=${REDIS_CONNECTION_TIMEOUT}
      # Authentication
      - JWT_SECRET=${JWT_SECRET}
    volumes:
      # Mount source code for hot reload
      - ./src:/usr/src/app/src:ro
      - ./prisma:/usr/src/app/prisma:ro
      - ./package.json:/usr/src/app/package.json:ro
      - ./tsconfig.json:/usr/src/app/tsconfig.json:ro
      # Exclude node_modules to use container's version
      - /usr/src/app/node_modules
    ports:
      - "${PORT}:${PORT}"
      - "5556:5555"  # Prisma Studio
    depends_on:
      db:
        condition: service_healthy
      # redis:
        # condition: service_healthy
    networks:
      - getrankt-dev-network

  # Vote Consumer (Development)
  # vote-consumer:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.dev
  #     target: development
  #   container_name: getrankt-consumer-dev
  #   restart: unless-stopped
  #   command: ["npm", "run", "redis:consumer:dev"]
  #   environment:
  #     - NODE_ENV=${NODE_ENV}
  #     - SERVICE_TYPE=consumer
  #     # Database configuration
  #     - DB_USER=${DB_USER}
  #     - DB_PASSWORD=${DB_PASSWORD}
  #     - DB_HOST=${DB_HOST}
  #     - DB_PORT=${DB_PORT}
  #     - DB_NAME=${DB_NAME}
  #     - DB_SCHEMA=${DB_SCHEMA}
  #     - DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@db:${DB_PORT}/${DB_NAME}?schema=${DB_SCHEMA}
  #     - DB_CONNECTION_LIMIT=${DB_CONNECTION_LIMIT}
  #     - DB_POOL_TIMEOUT=${DB_POOL_TIMEOUT}
  #     # Redis configuration
  #     - REDIS_URL=${REDIS_URL}
  #     - REDIS_RETRY_ATTEMPTS=${REDIS_RETRY_ATTEMPTS}
  #     - REDIS_RETRY_DELAY=${REDIS_RETRY_DELAY}
  #     - REDIS_CONNECTION_TIMEOUT=${REDIS_CONNECTION_TIMEOUT}
  #     # Authentication
  #     - JWT_SECRET=${JWT_SECRET}
  #   volumes:
  #     # Mount source code for hot reload
  #     - ./src:/usr/src/app/src:ro
  #     - ./scripts:/usr/src/app/scripts:ro
  #     - ./prisma:/usr/src/app/prisma:ro
  #     - ./package.json:/usr/src/app/package.json:ro
  #     - ./tsconfig.json:/usr/src/app/tsconfig.json:ro
  #     # Exclude node_modules to use container's version
  #     - /usr/src/app/node_modules
  #   depends_on:
  #     db:
  #       condition: service_healthy
  #     redis:
  #       condition: service_healthy
  #   networks:
  #     - getrankt-dev-network



volumes:
  pgdata_dev:
    driver: local
  # redis_data_dev:
    # driver: local

networks:
  getrankt-dev-network:
    driver: bridge
