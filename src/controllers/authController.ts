import { Request, Response } from 'express'
import {
  hashPassword,
  comparePassword,
  generateAccessToken,
  generateRefreshToken,
  getRefreshTokenExpiry,
  isRefreshTokenExpired,
  isW<PERSON>in<PERSON><PERSON><PERSON>eriod,
  TOKEN_ROTATION_CONFIG
} from '../utils/auth'
import { sendSuccess } from '../utils/responseWrapper'
import { DatabaseOperations } from '../database/DatabaseOperations'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logError, logInfo, logSecurityError } from '../utils/errorLogger'
import {
  ConflictError,
  UnauthorizedError,
  NotFoundError,
  ValidationError
} from '../middlewares/errorMiddleware'

const signup = async (req: Request, res: Response): Promise<void> => {
  const { email, password, username } = req.body
  try {
    // Input validation is now handled by middleware
    // Check if email already exists
    const existingEmail = await DatabaseOperations.findUserByEmail(email)
    if (existingEmail) {
      logSecurityError('Signup attempt with existing email',
        new Error('Email already exists'), req, { attemptType: 'signup_duplicate_email' })
      throw new ConflictError('Email already exists')
    }

    // Check if username already exists
    const existingUsername = await DatabaseOperations.findUserByUsername(username)
    if (existingUsername) {
      logSecurityError('Signup attempt with existing username',
        new Error('Username already exists'), req, { attemptType: 'signup_duplicate_username' })
      throw new ConflictError('Username already exists')
    }

    const passwordHash = await hashPassword(password)
    const user = await DatabaseOperations.createUser({
      email, passwordHash, username
    })
    logInfo('User created successfully', `User created: ${user.id}`, req, { userId: user.id })

    // Generate both access and refresh tokens
    const accessToken = generateAccessToken(user.id)
    const refreshToken = generateRefreshToken()
    const refreshTokenExpiry = getRefreshTokenExpiry()

    // Revoke any existing refresh tokens for this user (optional: limit concurrent sessions)
    // Comment out the next line if you want to allow multiple concurrent sessions
    // await DatabaseOperations.revokeAllUserRefreshTokens(user.id)

    // Store refresh token in database
    await DatabaseOperations.createRefreshToken(user.id, refreshToken, refreshTokenExpiry)

    sendSuccess(
      res,
      {
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          username: user.username
        }
      },
      'User registered successfully',
      HttpStatusCode.CREATED
    )
  } catch (err) {
    logError('Signup failed', err, req, { attemptType: 'signup_system_error' })
    throw err
  }
}

const login = async (req: Request, res: Response): Promise<void> => {
  const { email, password } = req.body
  try {
    // Input validation is now handled by middleware
    const user = await DatabaseOperations.findUserByEmail(email)
    if (!user) {
      logSecurityError('Login attempt with non-existent email',
        new Error('User not found'), req, { attemptType: 'login_invalid_email' })
      // Use generic message to prevent user enumeration
      throw new UnauthorizedError('Invalid credentials')
    }

    const match = await comparePassword(password, user.passwordHash)
    if (!match) {
      logSecurityError('Login attempt with invalid password',
        new Error('Invalid credentials'), req, { attemptType: 'login_invalid_password' })
      throw new UnauthorizedError('Invalid credentials')
    }

    // Generate both access and refresh tokens
    const accessToken = generateAccessToken(user.id)
    const refreshToken = generateRefreshToken()
    const refreshTokenExpiry = getRefreshTokenExpiry()

    // Revoke any existing refresh tokens for this user (optional: limit concurrent sessions)
    // Comment out the next line if you want to allow multiple concurrent sessions
    // await DatabaseOperations.revokeAllUserRefreshTokens(user.id)

    // Store refresh token in database
    await DatabaseOperations.createRefreshToken(user.id, refreshToken, refreshTokenExpiry)

    sendSuccess(
      res,
      {
        accessToken,
        refreshToken,
        user: {
          id: user.id,
          email: user.email,
          username: user.username,
          avatarUrl: user.avatarUrl,
          coins: user.coins,
          leagueLevel: user.leagueLevel,
          createdAt: user.createdAt
        }
      },
      'Login successful'
    )
    logInfo('User logged in successfully', `User logged in: ${user.id}`, req, { userId: user.id })
  } catch (err) {
    logError('Login failed', err, req, { attemptType: 'login_system_error' })
    throw err
  }
}

const getCurrentUser = async (req: Request, res: Response): Promise<void> => {
  const userId = (req as any).userId

  try {
    const user = await DatabaseOperations.findUserByIdWithSelect(userId, {
      id: true,
      email: true,
      username: true,
      avatarUrl: true,
      coins: true,
      leagueLevel: true,
      createdAt: true
    })

    if (!user) {
      logError('Current user not found', new Error('User not found'), req, { userId })
      throw new NotFoundError('User not found')
    }
    logInfo('Current user fetched successfully', `Current user fetched: ${user.id}`, req, { userId: user.id })
    sendSuccess(res, { user }, 'Current user fetched successfully')
  } catch (err) {
    logError('Error fetching current user', err, req, { userId })
    throw err
  }
}

const checkUsernameAvailability = async (req: Request, res: Response): Promise<void> => {
  const { username } = req.params

  try {

    const existingUser = await DatabaseOperations.findUserByUsername(username)
    const isAvailable = !existingUser

    sendSuccess(res, { available: isAvailable }, isAvailable ? 'Username is available' : 'Username is taken')
  } catch (err) {
    logError('Username availability check failed', err, req)
    throw err
  }
}

const getUserProfile = async (req: Request, res: Response): Promise<void> => {
  const { userId } = req.body

  try {
    // Validate that user ID is provided
    if (!userId) {
      throw new ValidationError('User ID is required')
    }

    // Validate that userId is a valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (typeof userId !== 'string' || !uuidRegex.test(userId)) {
      throw new ValidationError('Invalid user ID format')
    }

    const foundUser = await DatabaseOperations.findUserByIdWithSelect(userId, {
      id: true,
      username: true,
      avatarUrl: true,
      leagueLevel: true,
      createdAt: true
    })

    if (!foundUser) {
      logError('User profile not found', new Error('User not found'), req, { requestedUserId: userId })
      throw new NotFoundError('User not found')
    }

    logInfo('Public user profile fetched successfully', `Public profile fetched: ${foundUser.id}`, req, {
      userId: foundUser.id
    })
    sendSuccess(res, { user: foundUser }, 'User profile fetched successfully')
  } catch (err) {
    logError('Error fetching user profile', err, req, { requestedUserId: userId })
    throw err
  }
}

const refreshToken = async (req: Request, res: Response): Promise<void> => {
  const { refreshToken: token } = req.body

  try {
    if (!token) {
      throw new ValidationError('Refresh token is required')
    }

    // Find refresh token in database
    const storedToken = await DatabaseOperations.findRefreshToken(token)

    if (!storedToken) {
      logSecurityError('Refresh token not found',
        new Error('Invalid refresh token'), req, { attemptType: 'refresh_invalid_token' })
      throw new UnauthorizedError('Invalid refresh token')
    }

    // Check if token is expired
    if (isRefreshTokenExpired(storedToken.expiresAt)) {
      logSecurityError('Expired refresh token used',
        new Error('Refresh token expired'), req, { attemptType: 'refresh_expired_token' })

      // Check if within grace period for automatic rotation
      if (!isWithinGracePeriod(storedToken.expiresAt)) {
        // Beyond grace period - require re-login
        throw new UnauthorizedError('Refresh token expired - please login again')
      }

      // Within grace period - allow rotation but log security event
      logSecurityError('Refresh token used within grace period',
        new Error('Token expired but within grace period'), req, {
          attemptType: 'refresh_grace_period',
          userId: storedToken.userId
        })
    }

    // Check if token is revoked
    if (storedToken.isRevoked) {
      logSecurityError('Revoked refresh token used',
        new Error('Refresh token revoked'), req, { attemptType: 'refresh_revoked_token' })
      throw new UnauthorizedError('Refresh token revoked')
    }

    // Generate new access token
    const newAccessToken = generateAccessToken(storedToken.userId)

    // Always generate new refresh token (automatic rotation)
    const newRefreshToken = generateRefreshToken()
    const newRefreshTokenExpiry = getRefreshTokenExpiry()

    // Perform atomic token rotation
    await DatabaseOperations.rotateRefreshToken(
      token,
      storedToken.userId,
      newRefreshToken,
      newRefreshTokenExpiry
    )

    // Optional: Cleanup expired tokens for this user
    if (TOKEN_ROTATION_CONFIG.AUTO_CLEANUP) {
      // Run cleanup in background (don't await to avoid slowing down response)
      DatabaseOperations.cleanupExpiredRefreshTokens().catch(err => {
        logError('Background token cleanup failed', err, req, { userId: storedToken.userId })
      })
    }

    logInfo('Token refreshed successfully', `Token refreshed for user: ${storedToken.userId}`, req, {
      userId: storedToken.userId
    })

    sendSuccess(
      res,
      {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      },
      'Token refreshed successfully'
    )
  } catch (err) {
    logError('Token refresh failed', err, req, { attemptType: 'refresh_system_error' })
    throw err
  }
}

const logout = async (req: Request, res: Response): Promise<void> => {
  const { refreshToken: token } = req.body

  try {
    if (!token) {
      throw new ValidationError('Refresh token is required')
    }

    // Revoke the refresh token
    const storedToken = await DatabaseOperations.findRefreshToken(token)
    if (storedToken) {
      await DatabaseOperations.revokeRefreshToken(token)
      logInfo('User logged out successfully', `User logged out: ${storedToken.userId}`, req, {
        userId: storedToken.userId
      })
    }

    sendSuccess(res, {}, 'Logged out successfully')
  } catch (err) {
    logError('Logout failed', err, req, { attemptType: 'logout_system_error' })
    throw err
  }
}

const logoutAll = async (req: Request, res: Response): Promise<void> => {
  const userId = (req as any).userId

  try {
    // Revoke all refresh tokens for the user
    await DatabaseOperations.revokeAllUserRefreshTokens(userId)

    logInfo('User logged out from all devices', `User logged out from all devices: ${userId}`, req, {
      userId
    })

    sendSuccess(res, {}, 'Logged out from all devices successfully')
  } catch (err) {
    logError('Logout all failed', err, req, { attemptType: 'logout_all_system_error' })
    throw err
  }
}

export { signup, login, getCurrentUser, checkUsernameAvailability, getUserProfile, refreshToken, logout, logoutAll }
