import * as express from 'express'
import { signup, login, getCurrentUser, checkUsernameAvailability, getUserProfile, refreshToken, logout, logoutAll } from '../controllers/authController'
import { requireAuth } from '../middlewares/authMiddleware'
import {
  signupValidation,
  loginValidation
} from '../middlewares/validationMiddleware'
import {
  authRateLimit,
  loginRateLimit,
  signupRateLimit
} from '../middlewares/rateLimitMiddleware'
import {
  sanitizeInput,
  detectSuspiciousActivity
} from '../middlewares/securityMiddleware'

const router = express.Router()

// Apply security middleware to all auth routes
router.use(sanitizeInput)
router.use(detectSuspiciousActivity)

// Auth routes with validation and rate limiting
router.post('/signup',
  signupRateLimit,
  ...signupValidation,
  signup
)

router.post('/login',
  loginRateLimit,
  ...loginValidation,
  login
)

router.post('/refresh',
  authRateLimit,
  refreshToken
)

router.post('/logout',
  authRateLimit,
  logout
)

router.post('/logout-all',
  requireAuth,
  authRateLimit,
  logoutAll
)

router.get('/me', requireAuth, getCurrentUser)

router.get('/check-username/:username',
  authRateLimit,
  checkUsernameAvailability
)

router.post('/profile',
  requireAuth,
  getUserProfile
)

export default router
