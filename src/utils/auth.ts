import * as jwt from 'jsonwebtoken'
import * as bcrypt from 'bcrypt'
import * as crypto from 'crypto'

// Enhanced security configuration
console.log('🔍 Debug: Available environment variables:', Object.keys(process.env).filter(key => key.includes('JWT') || key.includes('SECRET')))
console.log('🔍 Debug: JWT_SECRET value:', process.env.JWT_SECRET ? '[PRESENT]' : '[MISSING]')
console.log('🔍 Debug: All env vars starting with J:', Object.keys(process.env).filter(key => key.startsWith('J')))

// Wait a moment for secrets to be available (Cloud Run sometimes needs this)
const getJWTSecret = () => {
  const secret = process.env.JWT_SECRET
  if (secret) {
    console.log('✅ JWT_SECRET found')
    return secret
  }

  console.error('❌ JWT_SECRET environment variable is missing')
  console.error('Available env vars:', Object.keys(process.env).sort())
  throw new Error('JWT_SECRET environment variable is required')
}

const JWT_SECRET = getJWTSecret()
const JWT_ALGORITHM = 'HS256'
const ACCESS_TOKEN_EXPIRY = '15m'  // 15 minutes for access token
const REFRESH_TOKEN_EXPIRY_DAYS = 7  // 7 days for refresh token
const REFRESH_TOKEN_GRACE_PERIOD_HOURS = 24  // 24 hours grace period after expiry
const BCRYPT_ROUNDS = 12  // Consider increasing from 10 to 12 for better security

// Token rotation configuration
export const TOKEN_ROTATION_CONFIG = {
  // Always rotate refresh tokens on use
  ALWAYS_ROTATE: true,
  // Grace period for expired tokens (in hours)
  GRACE_PERIOD_HOURS: REFRESH_TOKEN_GRACE_PERIOD_HOURS,
  // Maximum number of active refresh tokens per user (0 = unlimited)
  MAX_TOKENS_PER_USER: 5,
  // Automatically cleanup expired tokens on rotation
  AUTO_CLEANUP: true
}

export const hashPassword = (plain: string) => bcrypt.hash(plain, BCRYPT_ROUNDS)
export const comparePassword = (plain: string, hashed: string) => bcrypt.compare(plain, hashed)

// Generate access token (short-lived)
export const generateAccessToken = (userId: string) => {
  return jwt.sign({ userId, type: 'access' }, JWT_SECRET, {
    expiresIn: ACCESS_TOKEN_EXPIRY,
    algorithm: JWT_ALGORITHM
  })
}

// Generate refresh token (long-lived, cryptographically secure)
export const generateRefreshToken = (): string => {
  return crypto.randomBytes(64).toString('hex')
}

// Calculate refresh token expiry date
export const getRefreshTokenExpiry = (): Date => {
  const expiry = new Date()
  expiry.setDate(expiry.getDate() + REFRESH_TOKEN_EXPIRY_DAYS)
  return expiry
}

// Verify access token
export const verifyAccessToken = (token: string) => {
  return jwt.verify(token, JWT_SECRET, {
    algorithms: [JWT_ALGORITHM]
  })
}

// Legacy function for backward compatibility
export const generateToken = (userId: string) => {
  return generateAccessToken(userId)
}

// Legacy function for backward compatibility
export const verifyToken = (token: string) => {
  return verifyAccessToken(token)
}

// Check if refresh token is within grace period
export const isWithinGracePeriod = (expiresAt: Date): boolean => {
  const gracePeriodExpiry = new Date(expiresAt.getTime() + (TOKEN_ROTATION_CONFIG.GRACE_PERIOD_HOURS * 60 * 60 * 1000))
  return new Date() <= gracePeriodExpiry
}

// Check if refresh token is expired
export const isRefreshTokenExpired = (expiresAt: Date): boolean => {
  return new Date() > expiresAt
}

// Get grace period expiry date
export const getGracePeriodExpiry = (originalExpiry: Date): Date => {
  return new Date(originalExpiry.getTime() + (TOKEN_ROTATION_CONFIG.GRACE_PERIOD_HOURS * 60 * 60 * 1000))
}
