import { DatabaseOperations } from '../database/DatabaseOperations'
import { logInfo, logError } from './errorLogger'

/**
 * Token cleanup utilities for managing refresh tokens
 */
export class TokenCleanup {
  
  /**
   * Clean up expired and revoked refresh tokens
   * This should be run periodically (e.g., daily via cron job)
   */
  static async cleanupExpiredTokens(): Promise<void> {
    try {
      const result = await DatabaseOperations.cleanupExpiredRefreshTokens()
      logInfo('Token cleanup completed', `Cleaned up ${result.count} expired/revoked tokens`)
    } catch (error) {
      logError('Token cleanup failed', error)
      throw error
    }
  }

  /**
   * Clean up tokens for a specific user (useful for account deletion)
   */
  static async cleanupUserTokens(userId: string): Promise<void> {
    try {
      await DatabaseOperations.revokeAllUserRefreshTokens(userId)
      logInfo('User token cleanup completed', `Revoked all tokens for user: ${userId}`)
    } catch (error) {
      logError('User token cleanup failed', error, undefined, { userId })
      throw error
    }
  }

  /**
   * Get token statistics (useful for monitoring)
   */
  static async getTokenStats(): Promise<{
    totalTokens: number
    expiredTokens: number
    revokedTokens: number
    activeTokens: number
  }> {
    try {
      // Note: These would need to be implemented in DatabaseOperations
      // For now, returning placeholder values
      return {
        totalTokens: 0,
        expiredTokens: 0,
        revokedTokens: 0,
        activeTokens: 0
      }
    } catch (error) {
      logError('Failed to get token statistics', error)
      throw error
    }
  }
}
