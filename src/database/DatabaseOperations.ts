import { prisma } from '../providers/PrismaProvider'
import { Prisma } from '@prisma/client'

/**
 * DatabaseOperations - Centralized database operations class
 * 
 * This class provides reusable database operations for all entities in the application.
 * It encapsulates common patterns and provides type-safe methods for database interactions.
 */
export class DatabaseOperations {
  
  // ==================== USER OPERATIONS ====================

  /**
   * Find user by ID
   */
  static async findUserById(id: string) {
    return await prisma.user.findUnique({
      where: { id }
    })
  }

  /**
   * Find user by email
   */
  static async findUserByEmail(email: string) {
    return await prisma.user.findUnique({
      where: { email }
    })
  }

  /**
   * Find user by username
   */
  static async findUserByUsername(username: string) {
    return await prisma.user.findUnique({
      where: { username }
    })
  }

  /**
   * Find user by ID with specific fields selected
   */
  static async findUserByIdWithSelect(id: string, select: Prisma.UserSelect) {
    return await prisma.user.findUnique({
      where: { id },
      select
    })
  }

  /**
   * Find user by email with specific fields selected
   */
  static async findUserByEmailWithSelect(email: string, select: Prisma.UserSelect) {
    return await prisma.user.findUnique({
      where: { email },
      select
    })
  }

  /**
   * Find user by username with specific fields selected
   */
  static async findUserByUsernameWithSelect(username: string, select: Prisma.UserSelect) {
    return await prisma.user.findUnique({
      where: { username },
      select
    })
  }

  /**
   * Create a new user
   */
  static async createUser(data: Prisma.UserCreateInput) {
    return await prisma.user.create({ data })
  }

  /**
   * Update user coins with increment/decrement
   */
  static async updateUserCoins(userId: string, amount: number, operation: 'increment' | 'decrement') {
    return await prisma.user.update({
      where: { id: userId },
      data: {
        coins: { [operation]: Math.abs(amount) }
      },
      select: {
        id: true,
        username: true,
        coins: true
      }
    })
  }

  // ==================== REFRESH TOKEN OPERATIONS ====================

  /**
   * Create a new refresh token
   */
  static async createRefreshToken(userId: string, token: string, expiresAt: Date) {
    return await prisma.refreshToken.create({
      data: {
        userId,
        token,
        expiresAt
      }
    })
  }

  /**
   * Find refresh token by token string
   */
  static async findRefreshToken(token: string) {
    return await prisma.refreshToken.findUnique({
      where: { token },
      include: { user: true }
    })
  }

  /**
   * Revoke a refresh token
   */
  static async revokeRefreshToken(token: string) {
    return await prisma.refreshToken.update({
      where: { token },
      data: { isRevoked: true }
    })
  }

  /**
   * Revoke all refresh tokens for a user
   */
  static async revokeAllUserRefreshTokens(userId: string) {
    return await prisma.refreshToken.updateMany({
      where: { userId },
      data: { isRevoked: true }
    })
  }

  /**
   * Clean up expired refresh tokens
   */
  static async cleanupExpiredRefreshTokens() {
    return await prisma.refreshToken.deleteMany({
      where: {
        OR: [
          { expiresAt: { lt: new Date() } },
          { isRevoked: true }
        ]
      }
    })
  }

  /**
   * Count active refresh tokens for a user
   */
  static async countUserActiveRefreshTokens(userId: string) {
    return await prisma.refreshToken.count({
      where: {
        userId,
        isRevoked: false,
        expiresAt: { gt: new Date() }
      }
    })
  }

  /**
   * Get oldest refresh tokens for a user (for cleanup when limit exceeded)
   */
  static async getOldestUserRefreshTokens(userId: string, limit: number) {
    return await prisma.refreshToken.findMany({
      where: {
        userId,
        isRevoked: false
      },
      orderBy: { createdAt: 'asc' },
      take: limit
    })
  }

  /**
   * Rotate refresh token (revoke old, create new) - atomic operation
   */
  static async rotateRefreshToken(
    oldToken: string,
    userId: string,
    newToken: string,
    newExpiresAt: Date
  ) {
    return await prisma.$transaction(async (tx) => {
      // Revoke old token
      await tx.refreshToken.update({
        where: { token: oldToken },
        data: { isRevoked: true }
      })

      // Create new token
      const newRefreshToken = await tx.refreshToken.create({
        data: {
          userId,
          token: newToken,
          expiresAt: newExpiresAt
        }
      })

      return newRefreshToken
    })
  }

  // ==================== TOURNAMENT OPERATIONS ====================

  /**
   * Create a new tournament
   */
  static async createTournament(data: Prisma.TournamentCreateInput) {
    return await prisma.tournament.create({ data })
  }

  /**
   * Find tournament by ID with optional includes
   */
  static async findTournamentById(
    id: string, 
    include?: Prisma.TournamentInclude
  ) {
    return await prisma.tournament.findUnique({
      where: { id },
      include
    })
  }

  /**
   * Find tournament with submission count
   */
  static async findTournamentWithCount(tournamentId: string) {
    return await prisma.tournament.findUnique({
      where: { id: tournamentId },
      include: {
        _count: {
          select: { submissions: true }
        }
      }
    })
  }

  /**
   * Find tournaments by status with submission counts and creator info
   */
  static async findTournamentsByStatusWithDetails(statuses: string[]) {
    return await prisma.tournament.findMany({
      where: {
        status: { in: statuses }
      },
      include: {
        createdBy: {
          select: { username: true }
        },
        _count: {
          select: { submissions: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * Update tournament
   */
  static async updateTournament(id: string, data: Prisma.TournamentUpdateInput) {
    return await prisma.tournament.update({
      where: { id },
      data
    })
  }

  /**
   * Delete tournament and related data in transaction
   */
  static async deleteTournamentWithRelatedData(tournamentId: string) {
    return await prisma.$transaction(async (tx) => {
      // Delete all submissions related to this tournament
      await tx.submission.deleteMany({
        where: { tournamentId }
      })

      // Delete all matchups related to this tournament
      await (tx as any).matchup.deleteMany({
        where: { tournamentId }
      })

      // Delete all votes related to this tournament
      await (tx as any).vote.deleteMany({
        where: { tournamentId }
      })

      // Delete all coin transactions related to this tournament
      await tx.coinTransaction.deleteMany({
        where: { relatedTournamentId: tournamentId }
      })

      // Finally, delete the tournament itself
      return await tx.tournament.delete({
        where: { id: tournamentId }
      })
    })
  }

  // ==================== SUBMISSION OPERATIONS ====================

  /**
   * Find submission by user and tournament
   */
  static async findSubmissionByUserAndTournament(userId: string, tournamentId: string) {
    return await prisma.submission.findFirst({
      where: { userId, tournamentId }
    })
  }

  /**
   * Create submission with coin transaction
   */
  static async createSubmissionWithCoinTransaction(
    userId: string,
    tournamentId: string,
    videoUrl: string,
    entryFee: number
  ) {
    return await prisma.$transaction(async (tx) => {
      // Create coin transaction record for tournament entry
      const coinTransaction = await tx.coinTransaction.create({
        data: {
          userId,
          type: 'TournamentEntry',
          amount: -entryFee,
          relatedTournamentId: tournamentId
        }
      })

      // Update user's coin balance
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          coins: { decrement: entryFee }
        },
        select: {
          id: true,
          username: true,
          coins: true
        }
      })

      // Create the submission
      const submission = await tx.submission.create({
        data: {
          userId,
          tournamentId,
          videoUrl,
        },
      })

      return { coinTransaction, updatedUser, submission }
    })
  }

  /**
   * Find submissions for tournament with user data
   */
  static async findSubmissionsForTournament(tournamentId: string) {
    return await prisma.submission.findMany({
      where: { tournamentId },
      include: {
        user: {
          select: { username: true, avatarUrl: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  /**
   * Find submissions by criteria with user data
   */
  static async findSubmissionsWithUserData(
    where: Prisma.SubmissionWhereInput,
    orderBy?: Prisma.SubmissionOrderByWithRelationInput[]
  ) {
    return await prisma.submission.findMany({
      where,
      include: {
        user: {
          select: {
            username: true,
            avatarUrl: true
          }
        }
      },
      orderBy
    })
  }

  /**
   * Update submission
   */
  static async updateSubmission(id: string, data: Prisma.SubmissionUpdateInput) {
    return await prisma.submission.update({
      where: { id },
      data
    })
  }

  /**
   * Count submissions by criteria
   */
  static async countSubmissions(where: Prisma.SubmissionWhereInput) {
    return await prisma.submission.count({ where })
  }

  /**
   * Find submission by ID with user data
   */
  static async findSubmissionByIdWithUser(submissionId: string) {
    return await prisma.submission.findUnique({
      where: { id: submissionId },
      include: {
        user: {
          select: {
            username: true,
            avatarUrl: true
          }
        }
      }
    })
  }

  /**
   * Find tournament winner submission
   */
  static async findTournamentWinner(tournamentId: string) {
    return await prisma.submission.findFirst({
      where: {
        tournamentId,
        status: 'winner'
      },
      include: {
        user: {
          select: {
            username: true,
            avatarUrl: true
          }
        }
      }
    })
  }

  /**
   * Find user submissions
   */
  static async getUserSubmissions(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        submissions: {
          select: {
            id: true,
            videoUrl: true,
            status: true,
            createdAt: true,
            tournament: {
              select: {
                id: true,
                title: true,
                category: true,
                status: true
              }
            }
          }
        }
      }
    })
    
    return user?.submissions || []
  }

  // ==================== COIN TRANSACTION OPERATIONS ====================

  /**
   * Create coin purchase transaction with user balance update
   */
  static async createCoinPurchaseTransaction(
    userId: string,
    amount: number
  ) {
    return await prisma.$transaction(async (tx) => {
      // Create coin transaction record
      const coinTransaction = await tx.coinTransaction.create({
        data: {
          userId,
          type: 'Purchase',
          amount,
          relatedTournamentId: null
        }
      })

      // Update user's coin balance
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          coins: { increment: amount }
        },
        select: {
          id: true,
          username: true,
          coins: true
        }
      })

      return { coinTransaction, updatedUser }
    })
  }

  /**
   * Create tournament entry transaction with user balance update
   */
  static async createTournamentEntryTransaction(
    userId: string,
    amount: number,
    tournamentId: string
  ) {
    return await prisma.$transaction(async (tx) => {
      // Create coin transaction record
      const coinTransaction = await tx.coinTransaction.create({
        data: {
          userId,
          type: 'TournamentEntry',
          amount: -Math.abs(amount), // Ensure negative amount for deduction
          relatedTournamentId: tournamentId
        }
      })

      // Update user's coin balance
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          coins: { decrement: Math.abs(amount) }
        },
        select: {
          id: true,
          username: true,
          coins: true
        }
      })

      return { coinTransaction, updatedUser }
    })
  }

  /**
   * Create reward transaction with user balance update
   */
  static async createRewardTransaction(
    userId: string,
    amount: number,
    tournamentId?: string
  ) {
    return await prisma.$transaction(async (tx) => {
      // Create coin transaction record
      const coinTransaction = await tx.coinTransaction.create({
        data: {
          userId,
          type: 'Reward',
          amount,
          relatedTournamentId: tournamentId
        }
      })

      // Update user's coin balance
      const updatedUser = await tx.user.update({
        where: { id: userId },
        data: {
          coins: { increment: amount }
        },
        select: {
          id: true,
          username: true,
          coins: true
        }
      })

      return { coinTransaction, updatedUser }
    })
  }

  /**
   * Get user's coin transaction history
   */
  static async getCoinTransactionHistory(userId: string) {
    return await prisma.coinTransaction.findMany({
      where: { userId },
      include: {
        relatedTournament: {
          select: {
            id: true,
            title: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })
  }

  // ==================== VOTE OPERATIONS ====================

  /**
   * Check if user has voted on a specific matchup
   */
  static async hasUserVotedOnMatchup(
    voterId: string,
    firstSubmissionId: string,
    secondSubmissionId: string
  ): Promise<boolean> {
    const existingVote = await (prisma as any).vote.findFirst({
      where: {
        voterId,
        OR: [
          { firstSubmissionId, secondSubmissionId },
          { firstSubmissionId: secondSubmissionId, secondSubmissionId: firstSubmissionId }
        ]
      }
    })
    return !!existingVote
  }

  /**
   * Create vote and update winner's vote count
   */
  static async createVoteWithUpdate(
    voterId: string,
    tournamentId: string,
    firstSubmissionId: string,
    secondSubmissionId: string,
    winnerSubmissionId: string,
    bracketRound: number
  ) {
    return await prisma.$transaction(async (tx) => {
      // Record the vote
      await (tx as any).vote.create({
        data: {
          voterId,
          tournamentId,
          firstSubmissionId,
          secondSubmissionId,
          winnerSubmissionId,
          bracketRound
        }
      })

      // Increment vote count for the winner
      const updatedWinner = await tx.submission.update({
        where: { id: winnerSubmissionId },
        data: {
          votes: { increment: 1 }
        }
      })

      // Get updated loser data
      const updatedLoser = await tx.submission.findUnique({
        where: { id: winnerSubmissionId === firstSubmissionId ? secondSubmissionId : firstSubmissionId }
      })

      return { updatedWinner, updatedLoser }
    })
  }

  // ==================== MATCHUP OPERATIONS ====================

  /**
   * Find matchups with submission data
   */
  static async findMatchupsWithSubmissionData(
    where: any,
    orderBy?: any[]
  ) {
    return await (prisma as any).matchup.findMany({
      where,
      include: {
        firstSubmission: {
          include: {
            user: {
              select: {
                username: true,
                avatarUrl: true
              }
            }
          }
        },
        secondSubmission: {
          include: {
            user: {
              select: {
                username: true,
                avatarUrl: true
              }
            }
          }
        }
      },
      orderBy
    })
  }

  /**
   * Create matchup
   */
  static async createMatchup(data: any) {
    return await (prisma as any).matchup.create({ data })
  }

  /**
   * Update matchup
   */
  static async updateMatchup(id: string, data: any) {
    return await (prisma as any).matchup.update({
      where: { id },
      data
    })
  }

  /**
   * Delete matchups by criteria
   */
  static async deleteMatchups(where: any) {
    return await (prisma as any).matchup.deleteMany({ where })
  }

  // ==================== COMPLEX QUERY OPERATIONS ====================

  /**
   * Find tournaments with active matchups for random selection
   */
  static async findTournamentsWithActiveMatchups(statuses: string[]) {
    return await prisma.tournament.findMany({
      where: {
        status: { in: statuses }
      },
      include: {
        matchups: {
          where: { isActive: true } as any,
          include: {
            firstSubmission: {
              include: {
                user: {
                  select: {
                    username: true,
                    avatarUrl: true
                  }
                }
              }
            },
            secondSubmission: {
              include: {
                user: {
                  select: {
                    username: true,
                    avatarUrl: true
                  }
                }
              }
            }
          }
        }
      }
    })
  }

  /**
   * Execute raw SQL query
   */
  static async executeRaw(query: any, ...params: any[]) {
    return await prisma.$executeRaw(query, ...params)
  }

  /**
   * Execute transaction
   */
  static async executeTransaction<T>(
    callback: (tx: any) => Promise<T>
  ): Promise<T> {
    return await prisma.$transaction(callback)
  }

  // ==================== TOURNAMENT BRACKET OPERATIONS ====================

  /**
   * Initialize tournament bracket (set to active and set current bracket)
   */
  static async initializeTournamentBracket(tournamentId: string, firstRound: number, tx?: any) {
    const client = tx || prisma
    return await client.tournament.update({
      where: { id: tournamentId },
      data: {
        status: 'active',
        currentBracket: firstRound
      }
    })
  }

  /**
   * Update tournament to next bracket
   */
  static async updateTournamentToNextBracket(tournamentId: string, nextBracket: number, tx?: any) {
    const client = tx || prisma
    return await client.tournament.update({
      where: { id: tournamentId },
      data: { currentBracket: nextBracket }
    })
  }

  /**
   * Complete tournament with winner
   */
  static async completeTournamentWithWinner(tournamentId: string, winnerId: string, tx?: any) {
    const client = tx || prisma

    // Update winner submission
    await client.submission.update({
      where: { id: winnerId },
      data: { status: 'winner' }
    })

    // Update tournament status
    await client.tournament.update({
      where: { id: tournamentId },
      data: { status: 'completed' }
    })
  }

  /**
   * Advance contestants to next bracket
   */
  static async advanceContestantsToNextBracket(
    contestants: any[],
    nextBracket: number,
    tx?: any
  ) {
    const client = tx || prisma

    for (const contestant of contestants) {
      await client.submission.update({
        where: { id: contestant.id },
        data: {
          currentBracket: nextBracket,
          status: 'active',
          bracketPosition: null
        }
      })
    }
  }

  /**
   * Eliminate contestants from tournament
   */
  static async eliminateContestants(contestants: any[], tx?: any) {
    const client = tx || prisma

    for (const contestant of contestants) {
      await client.submission.update({
        where: { id: contestant.id },
        data: {
          status: 'eliminated',
          eliminatedAt: new Date()
        }
      })
    }
  }

  /**
   * Assign bracket positions to contestants
   */
  static async assignBracketPositions(contestants: any[], tx?: any) {
    const client = tx || prisma

    for (let i = 0; i < contestants.length; i++) {
      await client.submission.update({
        where: { id: contestants[i].id },
        data: { bracketPosition: i + 1 }
      })
    }
  }

  /**
   * Find active contestants for bracket
   */
  static async findActiveContestantsForBracket(
    tournamentId: string,
    bracketRound: number,
    tx?: any
  ) {
    const client = tx || prisma
    return await client.submission.findMany({
      where: {
        tournamentId,
        currentBracket: bracketRound,
        status: 'active'
      },
      orderBy: [
        { votes: 'desc' },
        { createdAt: 'asc' }
      ]
    })
  }

  /**
   * Create bracket matchups from contestants
   */
  static async createBracketMatchups(
    contestants: any[],
    tournamentId: string,
    bracketRound: number,
    tx?: any
  ) {
    const client = tx || prisma

    for (let i = 0; i < contestants.length; i += 2) {
      if (i + 1 < contestants.length) {
        await client.matchup.create({
          data: {
            tournamentId,
            firstSubmissionId: contestants[i].id,
            secondSubmissionId: contestants[i + 1].id,
            bracketRound,
            isActive: true
          }
        })
      }
    }
  }

  /**
   * Deactivate bracket matchups
   */
  static async deactivateBracketMatchups(
    tournamentId: string,
    bracketRound: number,
    tx?: any
  ) {
    const client = tx || prisma
    return await client.matchup.updateMany({
      where: {
        tournamentId,
        bracketRound: bracketRound
      },
      data: { isActive: false }
    })
  }

  /**
   * Deactivate matchups for qualified contestants
   */
  static async deactivateMatchupsForQualifiedContestants(
    tournamentId: string,
    bracketRound: number,
    qualifiedIds: string[],
    tx?: any
  ) {
    const client = tx || prisma
    return await client.matchup.updateMany({
      where: {
        tournamentId,
        bracketRound: bracketRound,
        isActive: true,
        OR: [
          { firstSubmissionId: { in: qualifiedIds } },
          { secondSubmissionId: { in: qualifiedIds } }
        ]
      },
      data: { isActive: false }
    })
  }


}
