import { Request, Response, NextFunction } from 'express'
import { verifyAccessToken } from '../utils/auth'
import { HttpStatusCode } from '../enums/ErrorEnums'
import { logSecurityError } from '../utils/errorLogger'
import { UnauthorizedError } from './errorMiddleware'

export const requireAuth = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers.authorization
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    logSecurityError('Authentication failed: Missing or invalid Authorization header',
      new Error('Missing or invalid Authorization header'), req)
    throw new UnauthorizedError('Authentication required')
  }

  const token = authHeader.split(' ')[1]
  try {
    const decoded = verifyAccessToken(token) as { userId: string, type: string }

    // Ensure this is an access token
    if (decoded.type && decoded.type !== 'access') {
      logSecurityError('Authentication failed: Invalid token type',
        new Error('Invalid token type'), req)
      throw new UnauthorizedError('Invalid token type')
    }

    ;(req as any).userId = decoded.userId
    next()
  } catch (err: any) {
    // Provide specific error messages for different JWT errors
    if (err.name === 'TokenExpiredError') {
      logSecurityError('Authentication failed: Token expired', err, req)
      throw new UnauthorizedError('Access token expired')
    } else if (err.name === 'JsonWebTokenError') {
      logSecurityError('Authentication failed: Invalid token', err, req)
      throw new UnauthorizedError('Invalid access token')
    } else {
      logSecurityError('Authentication failed: Token verification error', err, req)
      throw new UnauthorizedError('Authentication failed')
    }
  }
}
